{"name": "fl-nextjs-core", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/material": "^7.2.0", "@mui/material-nextjs": "^7.2.0", "@tanstack/react-query": "5.81.2", "axios": "1.10.0", "jose": "6.0.11", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-feather": "^2.0.10", "react-hook-form": "^7.60.0", "zod": "3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}