"server-only";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { validateApiInput } from "@/_core/lib/service/validationService";
import { verifyToken } from "@/_core/lib/context/auth/authTokenService";

import { BaseHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { TokenInterface } from "../interface/";

import { UnauthorizedError } from "@/_core/lib/context/error/badRequestError";

export async function getTokenAction(body: FormData) {
  "use server";

  const cookiesStore = await cookies();
  const ObjectBody = Object.fromEntries(body);

  const builder = new BaseHttpRequestBuilder()
    .setUrl("/authentication_token")
    .setMethod("POST")
    .setBody(ObjectBody)
    .addBeforeBuild(() => {
      const result = validateApiInput(
        TokenInterface.tokenModel,
        "body",
        ObjectBody
      );
      builder.setBody(result);
    })
    .addAfterExecute((response) => {
      const result = validateApiInput(
        TokenInterface.tokenModel,
        "response",
        response.data
      );
      response.data = result;
    })
    .addAfterExecute(async (response) => {
      const payload = await verifyToken(response.data.token);
      cookiesStore.set("token", response.data.token, {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        expires: payload.exp ? new Date(payload.exp * 1000) : new Date(Date.now() + 1000 * 60 * 5),
      });
      cookiesStore.set("refresh_token", response.data.refresh_token, {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5),
      });
    })
    .addAfterExecute((response) => {
      redirect("/dashboard");
    });

  const response = await builder.run();
  return response.data;
}

export async function refreshTokenAction() {
  "use server";

  const cookiesStore = await cookies();
  const refreshToken = cookiesStore.get("refresh_token")?.value;

  if (!refreshToken) {
    throw new UnauthorizedError();
  }

  const builder = new BaseHttpRequestBuilder()
    .setUrl("/token/refresh")
    .setMethod("POST")
    .setBody({ refresh_token: refreshToken })
    .addBeforeBuild(() => {
      const result = validateApiInput(
        TokenInterface.refreshTokenModel,
        "body",
        { refresh_token: refreshToken }
      );
      builder.setBody(result);
    })
    .addAfterExecute((response) => {
      const result = validateApiInput(
        TokenInterface.refreshTokenModel,
        "response",
        response.data
      );
      response.data = result;
    })
    .addAfterExecute(async (response) => {
      const payload = await verifyToken(response.data.token);
      cookiesStore.set("token", response.data.token, {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        expires: payload.exp ? new Date(payload.exp * 1000) : new Date(Date.now() + 1000 * 60 * 5),
      });
    });

  const response = await builder.run();
  return response.data;
}
