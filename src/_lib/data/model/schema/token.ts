import { z } from "zod";

export const tokenSchemaBody = z.object({
  username: z
    .string()
    .min(3, "El número mínimo de caracteres para el nombre de usuario es 3")
    .max(20, "El número máximo de caracteres para el nombre de usuario es 20"),
  password: z
    .string()
    .min(3, "El número mínimo de caracteres para la contraseña es 3")
    .max(20, "El número máximo de caracteres para la contraseña es 20"),
});

export const tokenSchemaResponse = z.object({
  token: z.string(),
  refresh_token: z.string().optional().nullable(),
});

export const refreshTokenSchemaResponse = z.object({
  token: z.string(),
  refresh_token: z.string().optional().nullable(),
});

export const refreshTokenSchemaBody = z.object({
  refresh_token: z.string(),
});

export type TokenBody = z.infer<typeof tokenSchemaBody>;
export type TokenResponse = z.infer<typeof tokenSchemaResponse>;
export type RefreshTokenBody = z.infer<typeof refreshTokenSchemaBody>;
export type RefreshTokenResponse = z.infer<typeof refreshTokenSchemaResponse>;
