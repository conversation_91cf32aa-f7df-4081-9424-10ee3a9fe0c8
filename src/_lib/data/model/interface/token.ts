import { z } from "zod";
import { ApiModelBaseBuilder } from "@/_core/lib/data/interface/builder";

import {
  tokenSchemaBody,
  tokenSchemaResponse,
  refreshTokenSchemaBody,
  refreshTokenSchemaResponse,
} from "../schema/token";

export const tokenModel = new ApiModelBaseBuilder("login")
  .setBody(tokenSchemaBody)
  .setResponse(tokenSchemaResponse)
  .build();

export const refreshTokenModel = new ApiModelBaseBuilder("refreshToken")
  .setBody(refreshTokenSchemaBody)
  .setResponse(refreshTokenSchemaResponse)
  .build();
