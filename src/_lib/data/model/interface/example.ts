import { z } from "zod";
import { ApiModelCollectionBuilder, ApiModelIndexBuilder } from "@/_core/lib/data/interface/builder";

export const exampleCollection = new ApiModelCollectionBuilder()
.extendFiltersSchema(
    z.object({
        title: z.string()
        .min(3, 'El número mínimo de caracteres para título es 3')
        .max(20, 'El número máximo de caracteres para título es 20')
    })
)
.setResponseItemsSchema(
    z.object({
        title: z.string()
    })
)
.build();

export const exampleIndex = new ApiModelIndexBuilder()
.extendResponseSchema(
    z.object({
        title: z.string()
    })
)
.build();