"use client";

import { useEffect } from "react";
import { queryClient } from "@/_core/lib/provider/tanstackQuery/config/queryClient";

export function Notifiable({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  useEffect(() => {
    const unsubscribeQueryEvents = queryClient
      .getQueryCache()
      .subscribe((event) => {
        if (event.type === "updated") {
          const query = event.query;
          const { status, data, error } = query.state;

          if (status === "success") {

            throw new Error("No se pudo solucionar")
            console.log("Query Success", query.queryKey, data);
          }
          
          if (status === "error") {
            console.log("Query Error", query.queryKey, error);
          }
        }
      });
      
      // Suscripciones a otros eventos (mutaciones, etc.)
      
      
    return () => {
      // Atiende el evento y se desuscribe.
      unsubscribeQueryEvents();
    };
  }, []);

  return <>{children}</>;
}
