"use client";

import * as React from "react";
import { Box } from "@mui/material";
import { useFormContext, FieldValues, Path } from "react-hook-form";
import dynamic from "next/dynamic";
import { FieldMeta, FieldType } from "./types";

const PasswordFieldRenderer = dynamic(() => import("./fields/passwordFieldRenderer"));
const TextFieldRenderer = dynamic(() => import("./fields/textFieldRenderer"));
const CheckboxFieldRenderer = dynamic(() => import("./fields/checkboxFieldRenderer"));

const fieldRendererMap: Record<
  FieldType,
  (props: { name: string; config: FieldMeta }) => React.ReactElement
> = {
  text: (props) => <TextFieldRenderer {...props} />,
  email: (props) => <TextFieldRenderer {...props} />,
  number: (props) => <TextFieldRenderer {...props} />,
  checkbox: (props) => <CheckboxFieldRenderer {...props} />,
  password: (props) => <PasswordFieldRenderer {...props} />,
};

type FormFieldsProps<T extends FieldValues> = {
  meta: Record<keyof T & string, FieldMeta>;
};

export function FormFields<T extends FieldValues>({ meta }: FormFieldsProps<T>) {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      {Object.entries(meta).map(([name, config]) => {
        const Renderer = fieldRendererMap[config.type];
        return <Renderer key={name} name={name} config={config} />;
      })}
    </Box>
  );
}
