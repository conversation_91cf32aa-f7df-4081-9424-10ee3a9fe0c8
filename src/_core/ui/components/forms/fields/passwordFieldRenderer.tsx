"use client";

import {
  FormControl,
  InputLabel,
  OutlinedInput,
  InputAdornment,
  IconButton,
  Box,
} from "@mui/material";
import { Eye, EyeOff } from "react-feather";
import { useFormContext, FieldValues, Path } from "react-hook-form";
import { useState } from "react";
import { FieldMeta } from "../types";

export default function PasswordFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: string;
  config: FieldMeta;
}) {
  const { register, formState } = useFormContext<T>();
  const error = formState.errors[name as Path<T>];
  const [show, setShow] = useState(false);

  return (
    <FormControl 
        fullWidth 
        variant={config.variant ?? "outlined"}
        error={!!error}
        size={config.size ?? "medium"}
    >
      <InputLabel htmlFor={name}>{config.label}</InputLabel>
      <OutlinedInput
        id={name}
        type={show ? "text" : "password"}
        label={config.label}
        {...register(name as Path<T>)}
        endAdornment={
          <InputAdornment position="end">
            <IconButton onClick={() => setShow((s) => !s)} edge="end">
              {show ? <Eye /> : <EyeOff />}
            </IconButton>
          </InputAdornment>
        }
      />
      {error?.message && (
        <Box component="span" sx={{ color: "error.main", fontSize: 12, mt: 0.5, ml: 1.5 }}>
          {error.message.toString()}
        </Box>
      )}
    </FormControl>
  );
}
