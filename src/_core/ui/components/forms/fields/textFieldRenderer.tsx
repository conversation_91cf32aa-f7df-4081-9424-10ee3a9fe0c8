"use client";

import { TextField } from "@mui/material";
import { useFormContext, FieldValues, Path } from "react-hook-form";
import { FieldMeta } from "../types";

export default function TextFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: string;
  config: FieldMeta;
}) {
  const { register, formState } = useFormContext<T>();
  const error = formState.errors[name as Path<T>];

  return (
    <TextField
      fullWidth
      type={config.type}
      label={config.label}
      {...register(name as Path<T>)}
      error={!!error}
      helperText={error?.message?.toString()}
    />
  );
}
