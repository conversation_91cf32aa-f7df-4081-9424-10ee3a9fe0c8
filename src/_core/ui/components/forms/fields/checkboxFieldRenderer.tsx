"use client";

import { Checkbox, FormControlLabel, Typography } from "@mui/material";
import { useFormContext, FieldValues, Path } from "react-hook-form";
import { FieldMeta } from "../types";

export default function CheckboxFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: string;
  config: FieldMeta;
}) {
  const { register } = useFormContext<T>();

  return (
    <FormControlLabel
      control={<Checkbox {...register(name as Path<T>)} />}
      label={<Typography variant="body2">{config.label}</Typography>}
    />
  );
}
