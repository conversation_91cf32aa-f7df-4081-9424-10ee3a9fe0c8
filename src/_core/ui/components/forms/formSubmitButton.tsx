"use client";

import { Button } from "@mui/material";
import { useFormContext } from "react-hook-form";

export function FormSubmitButton({ label = "Enviar" }: { label?: string }) {
  const { formState } = useFormContext();
  const isValid = formState.isValid;

  return (
    <Button
      type="submit"
      variant="contained"
      color="primary"
      disabled={!isValid}
      sx={{ mt: 2 }}
      fullWidth
      className="rounded-xl"
    >
      {label}
    </Button>
  );
}
