"use client";

import { DefaultValues } from "react-hook-form";
import { ReactNode } from "react";
import { schemas, InferSchema, SchemaKeys } from "@/_lib/data/model/schema";
import { FormContextProvider } from "./formContextProvider";
import { FormFields } from "./formFields";
import { FieldMeta } from "./types";
import { LoadStaticDataError } from "@/_core/lib/context/error";

type ClientFormWrapperProps<K extends SchemaKeys> = {
  schemaId: K;
  meta: Record<keyof InferSchema<K>, FieldMeta>;
  defaultValues?: DefaultValues<InferSchema<K>>;
  onSubmit: (values: InferSchema<K>) => void;
  children?: ReactNode;
};

export function ClientFormWrapper<K extends SchemaKeys>({
  schemaId,
  meta,
  defaultValues,
  onSubmit,
  children,
}: ClientFormWrapperProps<K>) {
  const schema = schemas[schemaId];

  if(!schema) {
    throw new LoadStaticDataError(schemaId)
  }

  return (
    <FormContextProvider<InferSchema<K>>
      schema={schema}
      defaultValues={defaultValues}
      onSubmit={onSubmit}
    >
      <FormFields<InferSchema<K>> meta={meta} />
      {children}
    </FormContextProvider>
  );
}