"use client";

import { ReactNode } from "react";
import {
  useForm,
  FormProvider,
  FieldValues,
  DefaultValues,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ZodTypeAny } from "zod";

type FormContextProviderProps<T extends FieldValues> = {
  schema: ZodTypeAny;
  children: ReactNode;
  defaultValues?: DefaultValues<T>;
  onSubmit: (values: T) => void;
};

export function FormContextProvider<T extends FieldValues>({
  schema,
  children,
  defaultValues,
  onSubmit,
}: FormContextProviderProps<T>) {
  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onChange",
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>{children}</form>
    </FormProvider>
  );
}
