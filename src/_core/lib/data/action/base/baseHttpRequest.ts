/**
 * Request genérica utilizando Axios como intermediario
 */

import axios, { AxiosResponse, Method, ResponseType } from "axios";

/**
 * Tipado estricto para config de axios.
 *
 */
export interface StrictHttpActionParams<TRequest = any> {
  url: string;
  method: Method;

  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: TRequest;

  timeout?: number;
  responseType?: ResponseType;
  auth?: {
    username: string;
    password: string;
  };
  baseURL?: string;
  withCredentials?: boolean;
}

export class BaseHttpRequest<TRequest = any, TResponse = any> {
  protected readonly config: StrictHttpActionParams<TRequest>;

  constructor(config: StrictHttpActionParams<TRequest>) {
    this.config = config;
  }

  async execute(): Promise<AxiosResponse<TResponse>> {
    return axios.request<TResponse>(this.config);
  }
}
