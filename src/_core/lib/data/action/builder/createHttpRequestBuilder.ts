import { AxiosResponse } from "axios";

import { validateApiInput } from "@/_core/lib/service/validationService";

import { BaseHttpRequestBuilder } from "./baseHttpRequestBuilder";
import { BaseApiModel } from "@/_core/lib/data/interface/base/baseApiModel";

export class CreateHttpRequestBuilder<TRequest = any, TResponse = any> extends BaseHttpRequestBuilder<
  TRequest,
  TResponse
> {
  constructor() {
    super();
    this.setMethod("POST");
  }

  withValidation(model: BaseApiModel, fullMask?: boolean): this {
    this.addBeforeBuild(() => {
      const result = validateApiInput(model, "body", this["config"].data);
      if (fullMask) {
        this.setBody(result.data);
      }
    });

    this.addAfterExecute((response: AxiosResponse<TResponse>) => {
      const result = validateApiInput(model, "response", response.data);
      if (fullMask) {
        response.data = result.data;
      }
    });

    return this;
  }
}
