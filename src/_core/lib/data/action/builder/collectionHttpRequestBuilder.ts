import { AxiosResponse } from "axios";
import { BaseHttpRequestBuilder } from "./baseHttpRequestBuilder";
import { BaseApiModel } from "@/_core/lib/data/interface/base/baseApiModel";

import { validateApiInput } from "@/_core/lib/service/validationService";

export class CollectionHttpRequestBuilder<
  TResponse = any
> extends BaseHttpRequestBuilder<undefined, TResponse> {
  constructor() {
    super();
    this.setMethod("GET");
  }

  /**
   * Inyecta validación de filtros
   */
  withValidation(model: BaseApiModel, fullMask?: boolean): this {
    this.addBeforeBuild(() => {
      const result = validateApiInput(model, "filters", this["config"].params);
      if (fullMask) {
        this.setQueryParams(result.data);
      }
    });

    this.addAfterExecute((response: AxiosResponse<TResponse>) => {
      const result = validateApiInput(model, "response", response.data);
      if (fullMask) {
        response.data = result.data;
      }
    });

    return this;
  }
}
