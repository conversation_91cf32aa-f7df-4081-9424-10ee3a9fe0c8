import { AxiosResponse } from "axios";

import { validateApiInput } from "@/_core/lib/service/validationService";

import { BaseHttpRequestBuilder } from "./baseHttpRequestBuilder";
import { BaseApiModel } from "@/_core/lib/data/interface/base/baseApiModel";

export class EditHttpRequestBuilder<TRequest = any, TResponse = any> extends BaseHttpRequestBuilder<
  TRequest,
  TResponse
> {
  constructor() {
    super();
    this.setMethod("PUT"); // O PATCH
  }

  withValidation(model: BaseApiModel, fullMask?: boolean): this {
    this.addBeforeExecute(() => {
      const resultParams = validateApiInput(model, "params", this["config"].params);
      if (fullMask) {
        this.setQueryParams(resultParams.data);
      }

      const resultBody = validateApiInput(model, "body", this["config"].data);
      if (fullMask) {
        this.setBody(resultBody.data);
      }
    });

    this.addAfterExecute((response: AxiosResponse<TResponse>) => {
      const result = validateApiInput(model, "response", response.data);
      if (fullMask) {
        response.data = result.data;
      }
    });

    return this;
  }
}
