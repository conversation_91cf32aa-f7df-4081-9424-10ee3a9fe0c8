import { z, ZodObject } from 'zod';

export type SchemaType = ZodObject<any>

export class BaseSchemaModel {
  
  readonly _name: string;

  readonly schema: SchemaType;

  constructor(_name: string, schema: SchemaType = z.object({})) {
    this._name = _name;
    this.schema = schema;
  }

  validate(input: unknown) {
    return this.schema.safeParse(input);
  }

  parse(input: unknown) {
    return this.schema.parse(input);
  }
}