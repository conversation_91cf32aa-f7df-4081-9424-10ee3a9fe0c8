/**
 * Builder para endpoints de detalle (GET /items/:id).
 * Usa UUID por defecto como parámetro.
 */
import { z, ZodObject } from "zod";
import { ApiModelBaseBuilder } from "./ApiModelBaseBuilder";

export class ApiModelIndexBuilder extends ApiModelBaseBuilder {
  constructor(_name: string = 'index') {
    super(_name);
    this.setParams(
      z.object({
        id: z.string().uuid(),
      })
    );
    this.setResponse(
      z.object({
        id: z.string().uuid(),
      })
    );
  }

  extendResponseSchema(schema: ZodObject<any>) {
    const oldResponse = this.schemas.response!;

    const merged = oldResponse ? oldResponse.merge(schema) : schema;

    this.setResponse(merged);
    return this;
  }

  extendParamsSchema(schema: ZodObject<any>) {
    const oldParams = this.schemas.params!;

    const merged = oldParams ? oldParams.merge(schema) : schema;

    this.setParams(merged);
    return this;
  }
}
