/**
 * Builder para endpoints de creación (POST /items).
 * Incluye body y response con ID opcional.
 */
import { z, ZodObject } from "zod";
import { ApiModelBaseBuilder } from "./ApiModelBaseBuilder";

export class ApiModelCreateBuilder extends ApiModelBaseBuilder {
  constructor(_name: string = 'create') {
    super(_name);
    this.setParams(
      z.object({
        id: z.string().uuid(),
      })
    );
    this.setBody(z.object({}));
    this.setResponse(
      z.object({
        id: z.string().uuid(),
      })
    );
  }

  extendResponseSchema(schema: ZodObject<any>) {
    const oldResponse = this.schemas.response!;

    const merged = oldResponse ? oldResponse.merge(schema) : schema;

    this.setResponse(merged);
    return this;
  }

  extendParamsSchema(schema: ZodObject<any>) {
    const oldParams = this.schemas.params!;

    const merged = oldParams ? oldParams.merge(schema) : schema;

    this.setParams(merged);
    return this;
  }

  extendBodySchema(schema: ZodObject<any>) {
    const oldBody = this.schemas.body!;

    const merged = oldBody ? oldBody.merge(schema) : schema;

    this.setBody(merged);
    return this;
  }
}
