
export interface defaultErrorOptions extends ErrorOptions {
  code?: string;
  statusCode?: number;
  details?: unknown;
}

export class DefaultError extends Error {
  public readonly code?: string;
  public readonly statusCode?: number;
  public readonly details?: unknown;

  constructor(message: string, options: defaultErrorOptions = {}) {
    super(message, options);
    this.name = 'DefaultError';
    this.code = options.code || '500_01_DEFAULT';
    this.statusCode = options.statusCode || 500;
    this.details = options.details;

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toString(): string {
    const codePart = this.code ? `[${this.code}]` : '';
    const messagePart = this.message || '';
    const detailsPart = this.details
      ? ` – ${typeof this.details === 'object'
          ? Object.entries(this.details)[0]
          : String(this.details)}`
      : '';

    return `${codePart} ${messagePart}${detailsPart}`.trim();
  }
}
