import { DefaultError, defaultErrorOptions } from "../default";

export class LoadStaticDataError extends DefaultError {
  public readonly static_data_name: string;

  constructor(static_data_name: string, options: defaultErrorOptions = {}) {
    super(`Error al cargar datos estáticos: ${static_data_name}`, {
      code: options.code || '500_03_LOAD_STATIC_DATA',
      statusCode: options.statusCode || 500,
      details: options.details,
    });

    this.name = 'LoadStaticDataError';
    this.static_data_name = static_data_name;

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
