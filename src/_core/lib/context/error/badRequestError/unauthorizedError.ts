import { DefaultError, defaultErrorOptions } from "../default";

export class UnauthorizedError extends DefaultError {

  constructor(options: defaultErrorOptions = {}) {
    super(`No autorizado`, {
      code: options.code || '401_01_UNAUTHORIZED',
      statusCode: options.statusCode || 401,
      details: options.details,
    });

    this.name = 'UnauthorizedError';

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
