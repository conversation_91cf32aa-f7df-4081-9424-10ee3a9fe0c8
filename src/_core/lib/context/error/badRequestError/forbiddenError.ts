import { DefaultError, defaultErrorOptions } from "../default";

export class ForbiddenError extends DefaultError {

  constructor(options: defaultErrorOptions = {}) {
    super(`No cumple los permisos`, {
      code: options.code || '403_01_FORBIDDEN',
      statusCode: options.statusCode || 403,
      details: options.details,
    });

    this.name = 'ForbiddenError';

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
