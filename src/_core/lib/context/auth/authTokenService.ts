"server-only";
import { jwtVerify, importSPK<PERSON> } from "jose";

import { EnvDataNotFoundError, LoadStaticDataError, UnauthorizedError } from "@/_core/lib/context/error";

function getPublicKey() {
  try {
    const publicKeyPem = process.env.JWT_PUBLIC_KEY;
    if (!publicKeyPem) {
      throw new EnvDataNotFoundError("JWT_PUBLIC_KEY");
    }
    return importSPKI(publicKeyPem!, "RS256");
  } catch (err) {
    throw new LoadStaticDataError("JWT_PUBLIC_KEY");
  }
}

export async function verifyToken(token: string) {
  try {
    const publicKey = await getPublicKey();
    const { payload } = await jwtVerify(token, publicKey, {
      algorithms: ["RS256"],
    });
    return payload;
  } catch (err) {
    throw new UnauthorizedError();
  }
}
