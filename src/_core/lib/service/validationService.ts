import { BaseApiModel, SchemaKind } from "@/_core/lib/data/interface/base/baseApiModel";

import { SchemaValidationError } from "@/_core/lib/context/error/badRequestError";

export function validateApiInput(model: BaseApiModel, kind: SchemaK<PERSON>, input: unknown) {
  const schema = model.getSchema(kind);
  const result = schema.validate(input);
  if (!result.success) {
    const err = result.error;
    
    const firstErrorName = err.issues[0].path[0].toString();
    const firstError = err.issues[0].message;

    throw new SchemaValidationError( firstError, { atribute_name: firstErrorName });
  }
  return result.data;
}
