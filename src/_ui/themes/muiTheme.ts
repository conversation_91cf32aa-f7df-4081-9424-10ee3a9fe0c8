// src/theme.ts
import { createTheme } from '@mui/material/styles';
import type { ThemeOptions } from '@mui/material/styles';

// Extensiones de tipos para h7 y breakpoint '2xl'
declare module '@mui/material/styles' {
  interface TypographyVariants {
    h7: React.CSSProperties;
  }

  interface TypographyVariantsOptions {
    h7?: React.CSSProperties;
  }

  interface BreakpointOverrides {
    xs: true;
    sm: true;
    md: true;
    lg: true;
    xl: true;
    '2xl': true;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    h7: true;
  }
}

export const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: {
      main: '#006963',
      contrastText: '#FFFFFF',
      dark: '#003734',
      light: '#E4FFFB',
    },
    secondary: {
      main: '#84C43C',
      dark: '#355D00',
      light: '#EFFFD8',
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#FFFFFF',
      paper: '#FFFFFF',
    },
    text: {
      primary: '#006963',
      secondary: '#69798b',
      disabled: '#8C9291',
    },
    error: {
      main: '#ba1a1a',
      light: '#FFDAD6',
      dark: '#93000A',
      contrastText: '#FFFFFF',
    },
    warning: {
      main: '#F1C21B',
    },
    info: {
      main: '#0244CF',
    },
    success: {
      main: '#25A249',
    },
    divider: '#B8C8DC',
  },
  typography: {
    fontFamily: 'var(--font-inter), system-ui, sans-serif',
  },
};

// Creación del tema con tipografía y breakpoints extendidos
export const materialTheme = createTheme({
  ...themeOptions,
  breakpoints: {
    values: {
      xs: 0,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      '2xl': 1536,
    },
  },
  typography: {
    ...themeOptions.typography,
    h1: { fontSize: '3rem', fontWeight: 700 },
    h2: { fontSize: '2.25rem', fontWeight: 600 },
    h3: { fontSize: '1.875rem', fontWeight: 600 },
    h4: { fontSize: '1.5rem', fontWeight: 600 },
    h5: { fontSize: '1.25rem', fontWeight: 500 },
    h6: { fontSize: '1rem', fontWeight: 500 },
    h7: { fontSize: '0.875rem', fontWeight: 500 },
    body1: { fontSize: '1rem' },
    body2: { fontSize: '0.875rem' },
  },
});
