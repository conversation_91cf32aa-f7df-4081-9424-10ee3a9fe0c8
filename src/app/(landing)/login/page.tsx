"use client";

import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";

import { useForm } from "react-hook-form";
import { useState } from "react";
import { useMediaQuery, useTheme } from "@mui/material";

import { tokenSchemaBody, TokenBody } from "@/_lib/data/model/schema/token";

import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  InputAdornment,
  Paper,
  TextField,
  Typography,
  IconButton,
  FormControl,
  InputLabel,
  OutlinedInput,
} from "@mui/material";

import { Eye, EyeOff } from "react-feather";

export default function LoginModal() {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up("md"));

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TokenBody>({
    resolver: zodResolver(tokenSchemaBody),
  });

  const [showPassword, setShowPassword] = useState(false);

  return (
    <div
      className="
      flex
      justify-center items-center
      h-screen"
    >
      <Paper
        elevation={10}
        className="
          overflow-y-hidden
          lg:w-115 md:w-95 w-75
          lg:h-155 md:h-135 h-95
          lg:px-12 md:px-9 px-8
          lg:pt-16 md:pt-19 pt-1
          lg:translate-y-0 md:translate-y-[-2.5rem] translate-y-[-0.5rem]
          rounded-2xl shadow-md drop-shadow-xl"
      >
        <Box
          className="
            flex flex-col items-center"
        >
          <Image
            className="block md:hidden"
            src="/bqn_logo.svg"
            alt="LogoXS"
            width={54}
            height={54}
          />
          <Image
            className="hidden md:block lg:hidden"
            src="/bqn_logo.svg"
            alt="LogoMD"
            width={69}
            height={69}
          />
          <Image
            className="hidden lg:block"
            src="/bqn_logo.svg"
            alt="LogoLG"
            width={84}
            height={84}
          />
          <Typography
            sx={{ typography: { lg: "h5", md: "h6", sm: "h6" } }}
            className="
              lg:mt-6 md:mt-3 mt-4
              font-semibold text-center text-green-900"
          >
            Colegio de Bioquímicos NQN
          </Typography>
          <Typography
            sx={{ typography: { lg: "h6", md: "h6", sm: "h6" } }}
            className="text-gray-500"
          >
            Iniciar sesión
          </Typography>
        </Box>
        <Box
          className="
            flex flex-col items-center 
            lg:mt-7 md:mt-3 mt-2
            md:mx-1"
        >
          <TextField
            fullWidth
            label="Usuario"
            {...register("username")}
            error={!!errors.username}
            helperText={errors.username?.message}
            variant="outlined"
            margin="normal"
            size={isMdUp ? "medium" : "small"}
          />

          <FormControl
            {...register("password")}
            error={!!errors.password}
            helperText={errors.password?.message}
            fullWidth 
            variant="outlined" 
            margin="normal"
            size={isMdUp ? "medium" : "small"}
          >
            <InputLabel htmlFor="password">Contraseña</InputLabel>
            <OutlinedInput
              id="password"
              type={showPassword ? "text" : "password"}
              endAdornment={
                <InputAdornment position="end">
                  <IconButton
                    aria-label={
                      showPassword
                        ? "Esconder contraseña"
                        : "Mostrar contraseña"
                    }
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <Eye /> : <EyeOff />}
                  </IconButton>
                </InputAdornment>
              }
              label="Password"
            />
          </FormControl>
        </Box>

        <Box
          className="
            grid
            lg:grid-cols-5
            items-center 
            lg:mt-4 md:mt-1
            mx-1"
        >
          <Box
            className="
              order-1
              lg:col-span-3 md:col-span-5"
          >
            <FormControlLabel
              className="
                text-gray-500"
              control={<Checkbox />}
              label={
                <Typography className="text-xs" color="textSecondary">
                  Recordar este dispositivo
                </Typography>
              }
            />
          </Box>
          <Box
            className="
              lg:order-2 md:order-3
              justify-self-center
              lg:col-span-2 md:col-span-5
              lg:mt-0 md:mt-3"
          >
            <a
              href="#"
              className="
                text-xs
                text-cyan-600
                hover:underline"
            >
              ¿Olvidaste tu contraseña?
            </a>
          </Box>
          <Box
            className="
              lg:order-3 md:order-2
              col-span-5 
              lg:mt-7 md:mt-2 
              mx-1"
          >
            <Button
              fullWidth
              variant="contained"
              color="primary"
              className="
                bg-emerald-700 hover:bg-emerald-800 
                lg:py-2 rounded-xl"
            >
              Ingresar
            </Button>
          </Box>
        </Box>
      </Paper>
    </div>
  );
}
