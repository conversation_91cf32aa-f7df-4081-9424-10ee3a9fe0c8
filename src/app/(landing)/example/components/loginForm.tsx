"use client";

import { ClientFormWrapper } from "@/_core/ui/components/forms/clientFormWrapper";
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema } from "@/_lib/data/model/schema";
import { FormSubmitButton } from "@/_core/ui/components/forms/formSubmitButton";

import {
  Box,
  Checkbox,
  FormControlLabel,
  Typography,
} from "@mui/material";

const meta: Record<keyof InferSchema<"token_body">, FieldMeta> = {
  username: { label: "Nombre de usuario", type: "text" },
  password: { label: "Contraseña", type: "password" },
};

export function LoginForm() {
  return (
    <Box
      className="mt-12"
    >
      <ClientFormWrapper
        schemaId="token_body"
        meta={meta}
        onSubmit={(data) => {
          console.log("Formulario enviado:", data);
        }}
      >
        <Box
          className="
              grid
              lg:grid-cols-5
              items-center 
              lg:mt-4 md:mt-1
              mx-1"
        >
          <Box
            className="
                order-1
                lg:col-span-3 md:col-span-5"
          >
            <FormControlLabel
              className="
                  text-gray-500"
              control={<Checkbox />}
              label={
                <Typography className="text-xs" color="textSecondary">
                  Recordar este dispositivo
                </Typography>
              }
            />
          </Box>
          <Box
            className="
                lg:order-2 md:order-3
                justify-self-center
                lg:col-span-2 md:col-span-5
                lg:mt-0 md:mt-3"
          >
            <a
              href="#"
              className="
                  text-xs
                  text-cyan-600
                  hover:underline"
            >
              ¿Olvidaste tu contraseña?
            </a>
          </Box>
          <Box
            className="
                lg:order-3 md:order-2
                col-span-5 
                lg:mt-7 md:mt-2 
                mx-1"
          >
            <div
              className="
            lg:py-2"
            >
              <FormSubmitButton label="Ingresar" />
            </div>
          </Box>
        </Box>
      </ClientFormWrapper>
    </Box>
  );
}
