import { LoginForm } from "./components/loginForm";

import Image from "next/image";

import {
  Box,
  Paper,
  Typography,
} from "@mui/material";

export default function Page() {
  return (
      <div
        className="
        flex
        justify-center items-center
        h-screen"
      >
        <Paper
          elevation={10}
          className="
            overflow-y-hidden
            lg:w-115 md:w-95 w-75
            lg:h-155 md:h-135 h-95
            lg:px-12 md:px-9 px-8
            lg:pt-16 md:pt-19 pt-1
            lg:translate-y-0 md:translate-y-[-2.5rem] translate-y-[-0.5rem]
            rounded-2xl shadow-md drop-shadow-xl"
        >
          <Box
            className="
              flex flex-col items-center"
          >
            <Image
              className="block md:hidden"
              src="/bqn_logo.svg"
              alt="LogoXS"
              width={54}
              height={54}
            />
            <Image
              className="hidden md:block lg:hidden"
              src="/bqn_logo.svg"
              alt="LogoMD"
              width={69}
              height={69}
            />
            <Image
              className="hidden lg:block"
              src="/bqn_logo.svg"
              alt="LogoLG"
              width={84}
              height={84}
            />
            <Typography
              sx={{ typography: { lg: "h5", md: "h6", sm: "h6" } }}
              className="
                lg:mt-6 md:mt-3 mt-4
                font-semibold text-center text-green-900"
            >
              Colegio de Bioquímicos NQN
            </Typography>
            <Typography
              sx={{ typography: { lg: "h6", md: "h6", sm: "h6" } }}
              className="text-gray-500"
            >
              Iniciar sesión
            </Typography>
          </Box>
          {/* Formulario */}
          <LoginForm />
        </Paper>
      </div>
    );
}
