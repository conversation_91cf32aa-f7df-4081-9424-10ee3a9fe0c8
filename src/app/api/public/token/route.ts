import { NextRequest, NextResponse } from "next/server";
import { getTokenAction } from "@/_lib/data/model/action/token";

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const response = await getTokenAction(formData);
    return NextResponse.json(response);
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || "Error en servidor" },
      { status: 400 }
    );
  }
}
