'server-only'

import { NextRequest, NextResponse } from "next/server";

import { privateApiController } from "@/_core/controller/private.api.controller";

import { ExampleInterface } from "@/_lib/data/model/interface";
import { ExampleAction } from "@/_lib/data/model/action";


export async function GET(request: NextRequest, { params } : {
    request: NextRequest,
    params?: ExampleInterface.ParamsType,
}) {
  return privateApiController({
    request,
    //params: params,
    //paramsSchema: ExampleInterface.paramsSchema,
    querySchema: ExampleInterface.querySchema,
    //bodySchema: ExampleInterface.bodySchema,
    action: async ({headers, id, query, body}) => {
      const result = await ExampleAction.getExampleById();
      return result;
    },
  });
}
