'use server';

import { FetchingError } from '@/_core/lib/context/error/fetching_error';
import { useQuery } from '@tanstack/react-query';
import { ExampleAction } from '@/_lib/data/model/action';
import axios, { AxiosError } from 'axios';

async function fetchExample() {
  'use server'
  try {
    const { data } = await axios.get('/api/private/example');
    return data;
  } catch (error) {
    // Re-lanzamos el error para que React Query lo capture
    
    if (axios.isAxiosError(error)) {
      throw new FetchingError(
        error.response?.data?.message || 'Error al obtener los datos'
      );
    }
    throw new Error('Error inesperado');
  }
}

export default async function Dashboard() {
  /*
  const { data, error, isLoading } = useQuery({
    queryKey: ['example'],
    queryFn: fetchExample,
    retry: false,
  });

  if (isLoading) return <div>Cargando...</div>;

  if (error instanceof Error) {
    return (
      <div style={{ color: 'red' }}>
        <strong>Error:</strong> {error.message}
      </div>
    );
  }
  */

  const data = await ExampleAction.getExampleById()

  return (
    <>
      <strong>Dashboard</strong>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </>
  );
}
