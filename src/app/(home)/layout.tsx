"use server";
import OneTabOnlyGuard from "@/_core/ui/components/oneTabOnlyGuard/oneTabOnlyGuard";

export default async function HomeLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex flex-col overflow-x-hidden min-h-screen">
      <OneTabOnlyGuard />
      <header
        className="
        sticky flex
        z-50 top-0
        items-center
        bg-purple-300 h-14 px-4"
      >
        Header
      </header>
      <div className="flex flex-1 flex-col lg:flex-row">
        <aside
          className="
        order-2 lg:order-1
        bg-pink-300 p-4 2xl:w-90 xl:w-74 lg:w-58 h-14 lg:h-auto lg:min-h-full"
        >
          <menu>
            Navegación lateral
          </menu>
        </aside>
        <main
          className="
        flex-1 order-1 lg:order-2
        bg-green-300 p-4"
        >
          {children}
        </main>
        <aside
          className="
        hidden 2xl:block order-3
        bg-transparent 2xl:w-[15vw] max-w-90 2xl:min-h-full 2xl:h-auto"
        >
          {/* Barra lateral derecha */}
        </aside>
      </div>
    </div>
  );
}
